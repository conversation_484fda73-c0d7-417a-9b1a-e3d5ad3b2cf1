import os
from flask import Flask, render_template, request, redirect, url_for, session, jsonify
import pandas as pd
from flask_sqlalchemy import SQLAlchemy
import random
import requests

app = Flask(__name__)
app.secret_key = 'supersecretkey'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///questions.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# 定义数据库模型
class Question(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    question = db.Column(db.String(500), nullable=False)
    option_a = db.Column(db.String(200), nullable=False)
    option_b = db.Column(db.String(200), nullable=False)
    option_c = db.Column(db.String(200), nullable=False)
    option_d = db.Column(db.String(200), nullable=False)
    correct_answer = db.Column(db.String(1), nullable=False)
    explanation = db.Column(db.String(500))

# 初始化数据库
def init_db():
    db.create_all()
    
    # 检查数据库是否为空
    if Question.query.count() == 0:
        # 尝试从Excel加载数据
        try:
            excel_path = os.path.join(os.path.dirname(__file__), 'question.xlsx')
            df = pd.read_excel(excel_path)
            
            for _, row in df.iterrows():
                q = Question(
                    question=row['题目'],
                    option_a=row['选项A'],
                    option_b=row['选项B'],
                    option_c=row['选项C'],
                    option_d=row['选项D'],
                    correct_answer=row['正确答案'],
                    explanation=row.get('解析', '暂无解析')
                )
                db.session.add(q)
            db.session.commit()
            print("成功从Excel导入数据到数据库")
        except Exception as e:
            print(f"导入数据失败: {str(e)}")

# 有道翻译API设置
YOUDAO_API_KEY = '2169c93fc554f979'  # 替换成你的API Key
YOUDAO_API_SECRET = 'wqNzRYWYxX0MlEtzcxQ8pLQkSrF9sORB'  # 替换成你的API Secret
YOUDAO_URL = 'https://openapi.youdao.com/api'

@app.route('/')
def index():
    return render_template('index.html')

if __name__ == '__main__':
    with app.app_context():
        init_db()
    app.run(debug=True)

# 在之前的基础上添加以下路由

@app.route('/start')
def start_quiz():
    # 获取所有题目并打乱顺序
    all_questions = Question.query.all()
    random.shuffle(all_questions)
    session['questions'] = [q.id for q in all_questions]
    session['answers'] = {}
    return redirect(url_for('show_question', index=0))

@app.route('/quiz/<int:index>')
def show_question(index):
    if 'questions' not in session or index >= len(session['questions']):
        return redirect(url_for('index'))
    
    question_id = session['questions'][index]
    question = Question.query.get(question_id)
    
    return render_template('quiz.html', 
                          current_index=index,
                          total_questions=len(session['questions']),
                          question=question)

@app.route('/translate', methods=['POST'])
def translate():
    text = request.json.get('text', '')
    
    # 有道翻译API调用 (需要替换为你的API密钥)
    # 申请步骤：https://ai.youdao.com/
    # 1. 注册有道智云账号
    # 2. 创建应用，获取应用ID和密钥
    # 3. 启用翻译服务
    
    # 这里是预留的API调用代码
    """
    salt = str(random.randint(1, 65536))
    sign_str = YOUDAO_API_KEY + text + salt + YOUDAO_API_SECRET
    sign = hashlib.md5(sign_str.encode()).hexdigest()
    
    params = {
        'q': text,
        'from': 'auto',
        'to': 'auto',
        'appKey': YOUDAO_API_KEY,
        'salt': salt,
        'sign': sign
    }
    
    response = requests.get(YOUDAO_URL, params=params)
    result = response.json()
    translation = result.get('translation', [''])[0] if result.get('translation') else '翻译失败'
    """
    
    # 模拟翻译结果（实际使用时请取消注释上面的代码）
    translation = f"模拟翻译结果: {text[::-1]}"
    
    return jsonify(translation=translation)

# 管理员功能
@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if username == 'admin' and password == 'admin':
            session['admin_logged_in'] = True
            return redirect(url_for('admin_edit'))
        else:
            return render_template('admin_login.html', error='用户名或密码错误')
    return render_template('admin_login.html')

@app.route('/admin/edit')
def admin_edit():
    if not session.get('admin_logged_in'):
        return redirect(url_for('admin_login'))
    
    questions = Question.query.all()
    return render_template('admin_edit.html', questions=questions)

@app.route('/admin/add', methods=['GET', 'POST'])
def add_question():
    if not session.get('admin_logged_in'):
        return redirect(url_for('admin_login'))
    
    if request.method == 'POST':
        new_question = Question(
            question=request.form['question'],
            option_a=request.form['option_a'],
            option_b=request.form['option_b'],
            option_c=request.form['option_c'],
            option_d=request.form['option_d'],
            correct_answer=request.form['correct_answer'],
            explanation=request.form['explanation']
        )
        db.session.add(new_question)
        db.session.commit()
        return redirect(url_for('admin_edit'))
    
    return render_template('edit_question.html')  # 需要创建这个模板

@app.route('/admin/edit/<int:id>', methods=['GET', 'POST'])
def edit_question(id):
    if not session.get('admin_logged_in'):
        return redirect(url_for('admin_login'))
    
    question = Question.query.get_or_404(id)
    
    if request.method == 'POST':
        question.question = request.form['question']
        question.option_a = request.form['option_a']
        question.option_b = request.form['option_b']
        question.option_c = request.form['option_c']
        question.option_d = request.form['option_d']
        question.correct_answer = request.form['correct_answer']
        question.explanation = request.form['explanation']
        db.session.commit()
        return redirect(url_for('admin_edit'))
    
    return render_template('edit_question.html', question=question)

@app.route('/admin/delete/<int:id>')
def delete_question(id):
    if not session.get('admin_logged_in'):
        return redirect(url_for('admin_login'))
    
    question = Question.query.get_or_404(id)
    db.session.delete(question)
    db.session.commit()
    return redirect(url_for('admin_edit'))