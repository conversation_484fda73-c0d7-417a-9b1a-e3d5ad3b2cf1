<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>答题中 - 大学生英语四级训练</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script>
        // 全局存储题目数据
        window.quizData = {
            currentIndex: {{ current_index }},
            totalQuestions: {{ total_questions }},
            question: {
                id: "{{ question.id }}",
                text: "{{ question.question | replace('"', '\\"') | safe }}",
                options: {
                    A: "{{ question.option_a | replace('"', '\\"') | safe }}",
                    B: "{{ question.option_b | replace('"', '\\"') | safe }}",
                    C: "{{ question.option_c | replace('"', '\\"') | safe }}",
                    D: "{{ question.option_d | replace('"', '\\"') | safe }}"
                },
                correctAnswer: "{{ question.correct_answer }}",
                explanation: "{{ (question.explanation or '暂无解析') | replace('"', '\\"') | safe }}"
            }
        };
        
        // 幽默反馈消息
        window.feedbackMessages = {
            praise: [
                "太棒了！你的英语水平简直6到飞起！",
                "哇塞！这题对你来说小菜一碟！",
                "厉害了！四级考试稳过！",
                "大神，请收下我的膝盖！",
                "这题答得漂亮！继续加油！",
                "英语四级见了你都要绕道走！",
                "你这英语水平，教英语老师都绰绰有余了！",
                "答对了！看来四级证书已经在向你招手了！",
                "完美！这水平不去考雅思可惜了！",
                "牛啊牛啊！这题都难不倒你！",
                "这题答得，连英国女王都为你点赞！",
                "答得漂亮！英语界需要你这样的人才！"
            ],
            encouragement: [
                "别灰心，四级考试稳过！",
                "差一点就对了，再想想！",
                "这道题有点难，下次一定行！",
                "失败是成功之母，继续努力！",
                "别放弃，你离成功只差一个选项的距离！",
                "这道题出得有点刁钻，不是你的问题！",
                "错了这道题，说明你有进步的空间！",
                "这题是给英语专业的人准备的，别在意！",
                "答错不可怕，可怕的是放弃！继续加油！",
                "这道题连英国女王都可能答错，别灰心！",
                "这题太难了，不是你的错，是题目的错！",
                "答错了？没关系，说明你还有提升的空间！"
            ]
        };
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>大学生英语四级训练</h1>
            <p id="progress-indicator">当前进度: 加载中...</p>
        </div>
        
        <div class="quiz-container">
            <div class="question-card">
                <h2>题目</h2>
                <p id="question-text">加载题目中...</p>
            </div>
            
            <form id="answer-form">
                <div class="options" id="options-container">
                    <!-- 选项将通过JS动态填充 -->
                </div>
                
                <div class="quiz-controls">
                    <button type="button" class="btn prev-btn" id="prev-btn">上一题</button>
                    <button type="button" class="btn check-btn" id="check-btn">查看答案</button>
                    <button type="button" class="btn next-btn" id="next-btn">下一题</button>
                </div>
            </form>
            
            <div id="result-panel" class="hidden">
                <div class="result-card">
                    <h2 id="result-title"></h2>
                    <p id="result-message"></p>
                    <div class="explanation">
                        <h3>题目解析</h3>
                        <p id="explanation-text">加载解析中...</p>
                    </div>
                    <button class="btn close-btn" id="close-btn">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='script.js') }}"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从全局变量获取数据
            const quizData = window.quizData;
            const feedback = window.feedbackMessages;
            
            // 更新进度指示器
            document.getElementById('progress-indicator').textContent = 
                `当前进度: ${quizData.currentIndex + 1}/${quizData.totalQuestions}`;
            
            // 显示题目
            document.getElementById('question-text').textContent = quizData.question.text;
            
            // 显示选项
            const optionsContainer = document.getElementById('options-container');
            optionsContainer.innerHTML = '';
            
            for (const [key, value] of Object.entries(quizData.question.options)) {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                
                const label = document.createElement('label');
                
                const input = document.createElement('input');
                input.type = 'radio';
                input.name = 'answer';
                input.value = key;
                label.appendChild(input);
                
                const optionLetter = document.createElement('span');
                optionLetter.className = 'option-letter';
                optionLetter.textContent = key;
                label.appendChild(optionLetter);
                
                // 添加空格
                const space = document.createTextNode(' ');
                label.appendChild(space);
                
                // 添加选项文本
                const optionText = document.createTextNode(value);
                label.appendChild(optionText);
                
                optionDiv.appendChild(label);
                optionsContainer.appendChild(optionDiv);
            }
            
            // 显示解析
            document.getElementById('explanation-text').textContent = quizData.question.explanation;
            
            // 设置按钮状态
            document.getElementById('prev-btn').disabled = (quizData.currentIndex === 0);
            document.getElementById('next-btn').disabled = (quizData.currentIndex === quizData.totalQuestions - 1);
            
            // 导航按钮事件
            document.getElementById('prev-btn').addEventListener('click', function() {
                if (quizData.currentIndex > 0) {
                    window.location.href = '/quiz/' + (quizData.currentIndex - 1);
                }
            });
            
            document.getElementById('next-btn').addEventListener('click', function() {
                if (quizData.currentIndex < quizData.totalQuestions - 1) {
                    window.location.href = '/quiz/' + (quizData.currentIndex + 1);
                }
            });
            
            // 检查答案
            document.getElementById('check-btn').addEventListener('click', function() {
                const selected = document.querySelector('input[name="answer"]:checked');
                if (!selected) {
                    alert('请选择一个答案！');
                    return;
                }
                
                const answer = selected.value;
                const correctAnswer = quizData.question.correctAnswer;
                const isCorrect = answer === correctAnswer;
                
                // 显示结果
                const resultTitle = document.getElementById('result-title');
                const resultMessage = document.getElementById('result-message');
                
                // 随机选择一条消息
                const praiseIndex = Math.floor(Math.random() * feedback.praise.length);
                const encouragementIndex = Math.floor(Math.random() * feedback.encouragement.length);
                
                if (isCorrect) {
                    resultTitle.textContent = '太棒了！';
                    resultTitle.className = 'correct';
                    resultMessage.innerHTML = `恭喜你答对了！<br>${feedback.praise[praiseIndex]}`;
                } else {
                    resultTitle.textContent = '再试一次！';
                    resultTitle.className = 'incorrect';
                    resultMessage.innerHTML = `正确答案是：${correctAnswer}。<br>${feedback.encouragement[encouragementIndex]}`;
                }
                
                document.getElementById('result-panel').classList.remove('hidden');
            });
            
            // 关闭结果面板
            document.getElementById('close-btn').addEventListener('click', function() {
                document.getElementById('result-panel').classList.add('hidden');
            });
        });
    </script>
</body>
</html>