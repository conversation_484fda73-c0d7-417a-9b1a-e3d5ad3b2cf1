/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #1e5799, #207cca);
    color: #333;
    line-height: 1.6;
    padding: 20px;
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.header {
    background: #2c3e50;
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin-bottom: 10px;
    font-size: 2.2rem;
}

.main-content {
    padding: 30px;
    text-align: center;
}

.banner {
    width: 100%;
    max-width: 300px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.btn {
    display: inline-block;
    padding: 12px 25px;
    background: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin: 10px;
}

.btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.start-btn {
    background: #27ae60;
    font-size: 1.2rem;
    padding: 15px 40px;
}

.start-btn:hover {
    background: #219653;
}

.admin-btn {
    background: #e74c3c;
}

.admin-btn:hover {
    background: #c0392b;
}

.footer {
    background: #ecf0f1;
    padding: 15px;
    text-align: center;
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* 答题页面样式 */
.quiz-container {
    padding: 20px;
}

.question-card {
    background: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 20px;
    margin-bottom: 25px;
    border-radius: 0 5px 5px 0;
}

.options {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.option {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f1f8ff;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.2s;
}

.option:hover {
    background: #e3f2fd;
}

.option input {
    margin-right: 15px;
}

.option-letter {
    display: inline-block;
    width: 28px;
    height: 28px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 28px;
    margin-right: 10px;
    font-weight: bold;
}

.quiz-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.prev-btn, .next-btn {
    width: 120px;
}

.check-btn {
    background: #9b59b6;
}

.check-btn:hover {
    background: #8e44ad;
}

/* 结果面板 */
#result-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.result-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 600px;
    width: 90%;
}

.result-card h2 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 2rem;
}

.correct {
    color: #27ae60;
}

.incorrect {
    color: #e74c3c;
}

.explanation {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.close-btn {
    display: block;
    margin: 20px auto 0;
    background: #7f8c8d;
}

.close-btn:hover {
    background: #95a5a6;
}

/* 管理员样式 */
.admin-container {
    max-width: 1000px;
}

.admin-login-card {
    max-width: 400px;
    margin: 50px auto;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background: #3498db;
    font-size: 1.1rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #2c3e50;
    color: white;
}

.admin-header h1 {
    margin: 0;
}

.home-btn, .add-btn {
    padding: 8px 15px;
    font-size: 0.9rem;
}

.question-list {
    padding: 20px;
}

.question-item {
    background: #f8f9fa;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.actions {
    display: flex;
    gap: 10px;
}

.edit-btn {
    background: #f39c12;
}

.edit-btn:hover {
    background: #e67e22;
}

.delete-btn {
    background: #e74c3c;
}

.delete-btn:hover {
    background: #c0392b;
}