// 划词翻译功能
document.addEventListener('mouseup', function(e) {
    const selectedText = window.getSelection().toString().trim();
    if (selectedText.length > 0) {
        // 显示翻译加载提示
        showTranslationLoading(e.clientX, e.clientY);
        
        // 发送翻译请求
        fetch('/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ text: selectedText })
        })
        .then(response => response.json())
        .then(data => {
            if (data.translation) {
                showTranslation(e.clientX, e.clientY, data.translation);
            } else {
                showTranslation(e.clientX, e.clientY, "翻译失败，请重试");
            }
        })
        .catch(error => {
            showTranslation(e.clientX, e.clientY, "翻译服务不可用");
        });
    }
});

// 显示翻译加载提示
function showTranslationLoading(x, y) {
    const tooltip = document.createElement('div');
    tooltip.id = 'translation-tooltip';
    tooltip.style.position = 'fixed';
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
    tooltip.style.backgroundColor = '#3498db';
    tooltip.style.color = 'white';
    tooltip.style.padding = '5px 10px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.zIndex = '10000';
    tooltip.style.transform = 'translateY(-100%)';
    tooltip.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    tooltip.textContent = '翻译中...';
    
    document.body.appendChild(tooltip);
}

// 显示翻译结果
function showTranslation(x, y, text) {
    // 移除加载提示
    const loadingTooltip = document.getElementById('translation-tooltip');
    if (loadingTooltip) {
        loadingTooltip.remove();
    }
    
    // 创建翻译结果提示
    const tooltip = document.createElement('div');
    tooltip.id = 'translation-tooltip';
    tooltip.style.position = 'fixed';
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
    tooltip.style.backgroundColor = '#2c3e50';
    tooltip.style.color = 'white';
    tooltip.style.padding = '10px 15px';
    tooltip.style.borderRadius = '6px';
    tooltip.style.zIndex = '10000';
    tooltip.style.transform = 'translateY(-100%)';
    tooltip.style.boxShadow = '0 3px 10px rgba(0,0,0,0.3)';
    tooltip.style.maxWidth = '300px';
    tooltip.style.fontSize = '14px';
    tooltip.style.lineHeight = '1.5';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    // 3秒后自动关闭
    setTimeout(() => {
        tooltip.remove();
    }, 3000);
}

// 点击页面关闭翻译提示
document.addEventListener('mousedown', function(e) {
    const tooltip = document.getElementById('translation-tooltip');
    if (tooltip && !tooltip.contains(e.target)) {
        tooltip.remove();
    }
});