<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题目管理 - 大学生英语四级训练</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1>题目管理</h1>
            <a href="{{ url_for('index') }}" class="btn home-btn">返回首页</a>
            <a href="{{ url_for('add_question') }}" class="btn add-btn">添加新题</a>
        </div>
        
        <div class="question-list">
            {% for question in questions %}
            <div class="question-item">
                <div class="question-header">
                    <h3>题目 #{{ question.id }}</h3>
                    <div class="actions">
                        <a href="{{ url_for('edit_question', id=question.id) }}" class="btn edit-btn">编辑</a>
                        <a href="{{ url_for('delete_question', id=question.id) }}" class="btn delete-btn">删除</a>
                    </div>
                </div>
                <p>{{ question.question }}</p>
                <div class="options">
                    <p><strong>A:</strong> {{ question.option_a }}</p>
                    <p><strong>B:</strong> {{ question.option_b }}</p>
                    <p><strong>C:</strong> {{ question.option_c }}</p>
                    <p><strong>D:</strong> {{ question.option_d }}</p>
                </div>
                <p><strong>正确答案:</strong> {{ question.correct_answer }}</p>
                <p><strong>解析:</strong> {{ question.explanation or '无' }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>